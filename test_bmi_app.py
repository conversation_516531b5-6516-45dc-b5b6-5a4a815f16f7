#!/usr/bin/env python3
"""
Test script for BMI Database Application
This script tests the core functionality without GUI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import from the BMI Database module
exec(open('BMI Database.py').read())
# Now we can use BMIDatabase class
from datetime import date

def test_bmi_database():
    print("Testing BMI Database functionality...")
    
    # Create database instance
    db = BMIDatabase("test_bmi.db")
    
    # Test BMI calculation
    print("\n1. Testing BMI Calculation:")
    test_cases = [
        (50, 160),   # Underweight
        (65, 170),   # Healthy
        (80, 170),   # Overweight
        (95, 170),   # Obese
        (120, 170),  # Extremely Obese
    ]
    
    for weight, height in test_cases:
        bmi = db.calculate_bmi(weight, height)
        category = db.get_bmi_category(bmi)
        print(f"Weight: {weight}kg, Height: {height}cm -> BMI: {bmi:.1f} ({category})")
    
    # Test participant registration
    print("\n2. Testing Participant Registration:")
    participant_id = db.add_participant("John Doe", 25, 175.0)
    print(f"Registered participant with ID: {participant_id}")
    
    # Test weight record addition
    print("\n3. Testing Weight Record Addition:")
    success = db.add_weight_record(participant_id, 70.5)
    print(f"Added weight record: {'Success' if success else 'Failed'}")
    
    # Test participant search
    print("\n4. Testing Participant Search:")
    participants = db.search_participants("John")
    print(f"Found {len(participants)} participants matching 'John'")
    for p in participants:
        print(f"  ID: {p[0]}, Name: {p[1]}, Age: {p[2]}, Height: {p[3]}cm")
    
    # Test weight records retrieval
    print("\n5. Testing Weight Records Retrieval:")
    records = db.get_weight_records(participant_id)
    print(f"Found {len(records)} weight records for participant {participant_id}")
    for r in records:
        print(f"  Date: {r[5]}, Weight: {r[2]}kg, BMI: {r[3]:.1f}, Category: {r[4]}")
    
    print("\nAll tests completed successfully!")

if __name__ == "__main__":
    test_bmi_database()
