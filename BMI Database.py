import sys
import sqlite3
from datetime import datetime, date
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QLineEdit,
                             QFormLayout, QTableWidget, QTableWidgetItem,
                             QMessageBox, QDialog, QSpinBox, QDoubleSpinBox,
                             QDateEdit, QTextEdit, QScrollArea, QFrame,
                             QHeaderView, QComboBox)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont, QPalette, QColor

class BMIDatabase:
    def __init__(self, db_name="bmi_database.db"):
        self.db_name = db_name
        self.init_database()

    def init_database(self):
        """Initialize the SQLite database with required tables"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()

        # Create participants table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS participants (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                age INTEGER NOT NULL,
                height_cm REAL NOT NULL,
                registration_date DATE NOT NULL
            )
        ''')

        # Create weight_records table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS weight_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                participant_id INTEGER NOT NULL,
                weight_kg REAL NOT NULL,
                bmi REAL NOT NULL,
                bmi_category TEXT NOT NULL,
                record_date DATE NOT NULL,
                FOREIGN KEY (participant_id) REFERENCES participants (id)
            )
        ''')

        conn.commit()
        conn.close()

    def add_participant(self, name, age, height_cm):
        """Add a new participant to the database"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO participants (name, age, height_cm, registration_date)
            VALUES (?, ?, ?, ?)
        ''', (name, age, height_cm, date.today()))

        participant_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return participant_id

    def add_weight_record(self, participant_id, weight_kg, record_date=None):
        """Add a weight record for a participant"""
        if record_date is None:
            record_date = date.today()

        # Get participant height to calculate BMI
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()

        cursor.execute('SELECT height_cm FROM participants WHERE id = ?', (participant_id,))
        result = cursor.fetchone()

        if not result:
            conn.close()
            return False

        height_cm = result[0]
        bmi = self.calculate_bmi(weight_kg, height_cm)
        bmi_category = self.get_bmi_category(bmi)

        cursor.execute('''
            INSERT INTO weight_records (participant_id, weight_kg, bmi, bmi_category, record_date)
            VALUES (?, ?, ?, ?, ?)
        ''', (participant_id, weight_kg, bmi, bmi_category, record_date))

        conn.commit()
        conn.close()
        return True

    def get_participants(self):
        """Get all participants"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM participants ORDER BY name')
        participants = cursor.fetchall()

        conn.close()
        return participants

    def get_participant_by_id(self, participant_id):
        """Get participant by ID"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM participants WHERE id = ?', (participant_id,))
        participant = cursor.fetchone()

        conn.close()
        return participant

    def get_weight_records(self, participant_id):
        """Get all weight records for a participant"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM weight_records
            WHERE participant_id = ?
            ORDER BY record_date DESC
        ''', (participant_id,))

        records = cursor.fetchall()
        conn.close()
        return records

    def search_participants(self, search_term):
        """Search participants by name"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM participants
            WHERE name LIKE ?
            ORDER BY name
        ''', (f'%{search_term}%',))

        participants = cursor.fetchall()
        conn.close()
        return participants

    @staticmethod
    def calculate_bmi(weight_kg, height_cm):
        """Calculate BMI from weight (kg) and height (cm)"""
        height_m = height_cm / 100
        return weight_kg / (height_m ** 2)

    @staticmethod
    def get_bmi_category(bmi):
        """Determine BMI category based on BMI value"""
        if bmi < 18.5:
            return "Underweight"
        elif 18.5 <= bmi < 25:
            return "Healthy"
        elif 25 <= bmi < 30:
            return "Overweight"
        elif 30 <= bmi < 35:
            return "Obese"
        else:
            return "Extremely Obese"


class RegisterParticipantDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Register New Participant")
        self.setModal(True)
        self.setFixedSize(400, 300)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Title
        title = QLabel("Register New Participant")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Form
        form_layout = QFormLayout()

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("Enter full name")
        form_layout.addRow("Name:", self.name_edit)

        self.age_spinbox = QSpinBox()
        self.age_spinbox.setRange(1, 120)
        self.age_spinbox.setValue(25)
        form_layout.addRow("Age:", self.age_spinbox)

        self.height_spinbox = QDoubleSpinBox()
        self.height_spinbox.setRange(50.0, 250.0)
        self.height_spinbox.setValue(170.0)
        self.height_spinbox.setSuffix(" cm")
        self.height_spinbox.setDecimals(1)
        form_layout.addRow("Height:", self.height_spinbox)

        self.weight_spinbox = QDoubleSpinBox()
        self.weight_spinbox.setRange(20.0, 300.0)
        self.weight_spinbox.setValue(70.0)
        self.weight_spinbox.setSuffix(" kg")
        self.weight_spinbox.setDecimals(1)
        form_layout.addRow("Initial Weight:", self.weight_spinbox)

        layout.addLayout(form_layout)

        # Buttons
        button_layout = QHBoxLayout()

        self.register_btn = QPushButton("Register")
        self.register_btn.clicked.connect(self.accept)
        self.register_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)

        button_layout.addWidget(self.register_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def get_participant_data(self):
        return {
            'name': self.name_edit.text().strip(),
            'age': self.age_spinbox.value(),
            'height_cm': self.height_spinbox.value(),
            'initial_weight': self.weight_spinbox.value()
        }


class SearchParticipantDialog(QDialog):
    def __init__(self, db, parent=None):
        super().__init__(parent)
        self.db = db
        self.selected_participant = None
        self.setWindowTitle("Search Participants")
        self.setModal(True)
        self.setFixedSize(500, 400)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Title
        title = QLabel("Search Participants")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Search box
        search_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Enter name to search...")
        self.search_edit.textChanged.connect(self.search_participants)

        search_btn = QPushButton("Search")
        search_btn.clicked.connect(self.search_participants)

        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(search_btn)
        layout.addLayout(search_layout)

        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["ID", "Name", "Age", "Height (cm)"])
        self.results_table.horizontalHeader().setStretchLastSection(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.doubleClicked.connect(self.select_participant)
        layout.addWidget(self.results_table)

        # Buttons
        button_layout = QHBoxLayout()

        self.select_btn = QPushButton("Select")
        self.select_btn.clicked.connect(self.select_participant)
        self.select_btn.setEnabled(False)
        self.select_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)

        button_layout.addWidget(self.select_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

        # Load all participants initially
        self.load_all_participants()

        # Connect selection change
        self.results_table.selectionModel().selectionChanged.connect(self.on_selection_changed)

    def search_participants(self):
        search_term = self.search_edit.text().strip()
        if search_term:
            participants = self.db.search_participants(search_term)
        else:
            participants = self.db.get_participants()

        self.populate_table(participants)

    def load_all_participants(self):
        participants = self.db.get_participants()
        self.populate_table(participants)

    def populate_table(self, participants):
        self.results_table.setRowCount(len(participants))

        for row, participant in enumerate(participants):
            self.results_table.setItem(row, 0, QTableWidgetItem(str(participant[0])))  # ID
            self.results_table.setItem(row, 1, QTableWidgetItem(participant[1]))       # Name
            self.results_table.setItem(row, 2, QTableWidgetItem(str(participant[2])))  # Age
            self.results_table.setItem(row, 3, QTableWidgetItem(str(participant[3])))  # Height

    def on_selection_changed(self):
        self.select_btn.setEnabled(len(self.results_table.selectedItems()) > 0)

    def select_participant(self):
        current_row = self.results_table.currentRow()
        if current_row >= 0:
            participant_id = int(self.results_table.item(current_row, 0).text())
            self.selected_participant = self.db.get_participant_by_id(participant_id)
            self.accept()


class AddWeightDialog(QDialog):
    def __init__(self, participant_name, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"Add Weight Record - {participant_name}")
        self.setModal(True)
        self.setFixedSize(350, 200)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Title
        title = QLabel("Add Weight Record")
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # Form
        form_layout = QFormLayout()

        self.weight_spinbox = QDoubleSpinBox()
        self.weight_spinbox.setRange(20.0, 300.0)
        self.weight_spinbox.setValue(70.0)
        self.weight_spinbox.setSuffix(" kg")
        self.weight_spinbox.setDecimals(1)
        form_layout.addRow("Weight:", self.weight_spinbox)

        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("Date:", self.date_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_layout = QHBoxLayout()

        self.add_btn = QPushButton("Add Record")
        self.add_btn.clicked.connect(self.accept)
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)

        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def get_weight_data(self):
        return {
            'weight': self.weight_spinbox.value(),
            'date': self.date_edit.date().toPython()
        }


class ParticipantDetailWindow(QMainWindow):
    def __init__(self, db, participant, parent=None):
        super().__init__(parent)
        self.db = db
        self.participant = participant
        self.setWindowTitle(f"Participant Details - {participant[1]}")
        self.setGeometry(200, 200, 800, 600)
        self.setup_ui()
        self.load_weight_records()

    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Header with participant info
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.Box)
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #f0f0f0;
                border: 2px solid #cccccc;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)

        name_label = QLabel(f"Name: {self.participant[1]}")
        name_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))

        info_layout = QHBoxLayout()
        age_label = QLabel(f"Age: {self.participant[2]} years")
        height_label = QLabel(f"Height: {self.participant[3]} cm")
        reg_date_label = QLabel(f"Registered: {self.participant[4]}")

        age_label.setFont(QFont("Arial", 12))
        height_label.setFont(QFont("Arial", 12))
        reg_date_label.setFont(QFont("Arial", 12))

        info_layout.addWidget(age_label)
        info_layout.addWidget(height_label)
        info_layout.addWidget(reg_date_label)
        info_layout.addStretch()

        header_layout.addWidget(name_label)
        header_layout.addLayout(info_layout)
        layout.addWidget(header_frame)

        # Add weight button
        add_weight_btn = QPushButton("Add New Weight Record")
        add_weight_btn.clicked.connect(self.add_weight_record)
        add_weight_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(add_weight_btn)

        # Weight records table
        records_label = QLabel("Weight Records")
        records_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(records_label)

        self.records_table = QTableWidget()
        self.records_table.setColumnCount(5)
        self.records_table.setHorizontalHeaderLabels(["Date", "Weight (kg)", "BMI", "Category", "Status"])

        # Set column widths
        header = self.records_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)

        self.records_table.setAlternatingRowColors(True)
        self.records_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        layout.addWidget(self.records_table)

    def load_weight_records(self):
        records = self.db.get_weight_records(self.participant[0])
        self.records_table.setRowCount(len(records))

        for row, record in enumerate(records):
            # record: (id, participant_id, weight_kg, bmi, bmi_category, record_date)
            date_item = QTableWidgetItem(record[5])
            weight_item = QTableWidgetItem(f"{record[2]:.1f}")
            bmi_item = QTableWidgetItem(f"{record[3]:.1f}")
            category_item = QTableWidgetItem(record[4])

            # Color code the category
            category_colors = {
                "Underweight": "#87CEEB",      # Light blue
                "Healthy": "#90EE90",          # Light green
                "Overweight": "#FFD700",       # Gold
                "Obese": "#FFA500",            # Orange
                "Extremely Obese": "#FF6347"   # Tomato
            }

            if record[4] in category_colors:
                category_item.setBackground(QColor(category_colors[record[4]]))

            # Status indicator (recent records)
            status = "Recent" if row < 3 else "Historical"
            status_item = QTableWidgetItem(status)

            self.records_table.setItem(row, 0, date_item)
            self.records_table.setItem(row, 1, weight_item)
            self.records_table.setItem(row, 2, bmi_item)
            self.records_table.setItem(row, 3, category_item)
            self.records_table.setItem(row, 4, status_item)

    def add_weight_record(self):
        dialog = AddWeightDialog(self.participant[1], self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            weight_data = dialog.get_weight_data()

            success = self.db.add_weight_record(
                self.participant[0],
                weight_data['weight'],
                weight_data['date']
            )

            if success:
                self.load_weight_records()  # Refresh the table
                QMessageBox.information(self, "Success", "Weight record added successfully!")
            else:
                QMessageBox.warning(self, "Error", "Failed to add weight record.")


class BMIMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.db = BMIDatabase()
        self.setWindowTitle("BMI Database - Weekly Weight Tracker")
        self.setGeometry(100, 100, 600, 400)
        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Title
        title_label = QLabel("BMI Database")
        title_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 20px;")
        layout.addWidget(title_label)

        # Subtitle
        subtitle_label = QLabel("Weekly Weight Tracker - Saturday Weigh-ins")
        subtitle_label.setFont(QFont("Arial", 14))
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("color: #7f8c8d; margin-bottom: 30px;")
        layout.addWidget(subtitle_label)

        # Main buttons container
        button_container = QWidget()
        button_layout = QVBoxLayout(button_container)
        button_layout.setSpacing(20)

        # Register button
        self.register_btn = QPushButton("Register New Participant")
        self.register_btn.clicked.connect(self.register_participant)
        self.register_btn.setMinimumHeight(60)
        self.register_btn.setFont(QFont("Arial", 16, QFont.Weight.Bold))

        # Search button
        self.search_btn = QPushButton("Search Participants")
        self.search_btn.clicked.connect(self.search_participants)
        self.search_btn.setMinimumHeight(60)
        self.search_btn.setFont(QFont("Arial", 16, QFont.Weight.Bold))

        button_layout.addWidget(self.register_btn)
        button_layout.addWidget(self.search_btn)

        # Center the buttons
        layout.addStretch()
        layout.addWidget(button_container)
        layout.addStretch()

        # Status bar
        self.statusBar().showMessage("Ready - BMI Database System")

    def apply_styles(self):
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)

        # Specific styles for register button
        self.register_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)

    def register_participant(self):
        dialog = RegisterParticipantDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_participant_data()

            if not data['name']:
                QMessageBox.warning(self, "Error", "Please enter a name.")
                return

            try:
                # Add participant to database
                participant_id = self.db.add_participant(
                    data['name'],
                    data['age'],
                    data['height_cm']
                )

                # Add initial weight record
                self.db.add_weight_record(
                    participant_id,
                    data['initial_weight']
                )

                QMessageBox.information(
                    self,
                    "Success",
                    f"Participant '{data['name']}' registered successfully!\nID: {participant_id}"
                )

                self.statusBar().showMessage(f"Registered: {data['name']}")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to register participant: {str(e)}")

    def search_participants(self):
        dialog = SearchParticipantDialog(self.db, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if dialog.selected_participant:
                # Open participant detail window
                detail_window = ParticipantDetailWindow(
                    self.db,
                    dialog.selected_participant,
                    self
                )
                detail_window.show()
                self.statusBar().showMessage(f"Viewing: {dialog.selected_participant[1]}")


def main():
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("BMI Database")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("BMI Tracker")

    # Create and show main window
    window = BMIMainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()